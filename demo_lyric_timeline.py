# -*- coding: utf-8 -*-
"""
LyricTimeline OOP重构演示脚本
展示新的面向对象设计的功能
"""

from pathlib import Path

def demo_basic_usage():
    """演示基本用法"""
    print("=== LyricTimeline基本用法演示 ===")
    
    from lyric_timeline import (
        LyricTimeline, LyricDisplayMode, LyricStyle,
        create_enhanced_timeline, create_simple_timeline
    )
    
    # 示例数据
    test_lyrics = [
        (0.0, "第一句歌词"),
        (3.0, "第二句歌词"),
        (6.0, "第三句歌词"),
        (9.0, "第四句歌词")
    ]
    
    print("1. 创建增强预览模式时间轴")
    enhanced_timeline = create_enhanced_timeline(test_lyrics, "chinese")
    print(f"   时间轴信息: {enhanced_timeline.get_info()}")
    
    print("\n2. 创建简单模式时间轴")
    simple_timeline = create_simple_timeline(test_lyrics, "english")
    print(f"   时间轴信息: {simple_timeline.get_info()}")
    
    print("\n3. 计算所需显示区域")
    video_width, video_height = 1280, 720
    enhanced_rect = enhanced_timeline.calculate_required_rect(video_width, video_height)
    simple_rect = simple_timeline.calculate_required_rect(video_width, video_height)
    
    print(f"   增强模式区域: {enhanced_rect}")
    print(f"   简单模式区域: {simple_rect}")
    
    print("\n4. 检查区域重叠")
    if enhanced_rect.overlaps_with(simple_rect):
        print("   WARNING: 两个时间轴的显示区域重叠!")
    else:
        print("   OK: 两个时间轴的显示区域不重叠")
    
    print("\n5. 动态切换显示模式")
    enhanced_timeline.set_display_mode(
        LyricDisplayMode.ENHANCED_PREVIEW,
        current_y_offset=-60,
        preview_y_offset=100
    )
    print("   已切换增强模式参数")

def demo_lrc_file_usage():
    """演示LRC文件用法"""
    print("\n=== LRC文件解析演示 ===")
    
    from lyric_timeline import LyricTimeline, LyricDisplayMode
    
    # 检查LRC文件
    lrc_path = Path("精武英雄/精武英雄 - 甄子丹.lrc")
    if not lrc_path.exists():
        print(f"   WARNING: LRC文件不存在: {lrc_path}")
        return
    
    print(f"1. 从LRC文件创建时间轴: {lrc_path}")
    timeline = LyricTimeline.from_lrc_file(
        str(lrc_path),
        language="chinese",
        display_mode=LyricDisplayMode.ENHANCED_PREVIEW
    )
    
    info = timeline.get_info()
    print(f"   歌词信息:")
    print(f"   - 语言: {info['language']}")
    print(f"   - 总行数: {info['total_lines']}")
    print(f"   - 总时长: {info['duration']:.1f}秒")
    print(f"   - 显示模式: {info['display_mode']}")
    
    print("\n2. 获取前30秒的歌词")
    filtered_lyrics = timeline.get_filtered_lyrics(30.0)
    print(f"   前30秒歌词行数: {len(filtered_lyrics)}")
    for i, (time, text) in enumerate(filtered_lyrics[:5]):  # 显示前5行
        print(f"   [{time:6.1f}s] {text}")
    if len(filtered_lyrics) > 5:
        print(f"   ... 还有 {len(filtered_lyrics) - 5} 行")

def demo_enhanced_generator_integration():
    """演示与EnhancedJingwuGenerator的集成"""
    print("\n=== EnhancedJingwuGenerator集成演示 ===")
    
    from enhanced_generator import EnhancedJingwuGenerator, LYRIC_TIMELINE_AVAILABLE
    
    print(f"1. LyricTimeline可用性: {LYRIC_TIMELINE_AVAILABLE}")
    
    if not LYRIC_TIMELINE_AVAILABLE:
        print("   ERROR: LyricTimeline不可用")
        return
    
    print("2. 创建生成器")
    generator = EnhancedJingwuGenerator(width=720, height=1280)
    
    # 测试数据
    test_lyrics = [
        (0.0, "测试歌词1"),
        (3.0, "测试歌词2"),
        (6.0, "测试歌词3")
    ]
    
    print("3. 通过生成器创建时间轴")
    timeline = generator.create_enhanced_video_timeline(test_lyrics, "chinese")
    
    print("4. 时间轴详细信息")
    info = timeline.get_info()
    for key, value in info.items():
        if key == 'strategy_info':
            print(f"   {key}: {value}")
        else:
            print(f"   {key}: {value}")
    
    print("5. 计算布局信息")
    rect = timeline.calculate_required_rect(720, 1280)
    print(f"   所需区域: x={rect.x}, y={rect.y}, w={rect.width}, h={rect.height}")

def demo_strategy_pattern():
    """演示策略模式"""
    print("\n=== 策略模式演示 ===")
    
    from lyric_timeline import LyricTimeline, LyricDisplayMode, LyricStyle
    
    test_lyrics = [
        (0.0, "策略测试1"),
        (3.0, "策略测试2")
    ]
    
    print("1. 创建时间轴并测试不同策略")
    timeline = LyricTimeline(test_lyrics, "test")
    
    strategies = [
        (LyricDisplayMode.SIMPLE_FADE, "简单淡入淡出"),
        (LyricDisplayMode.ENHANCED_PREVIEW, "增强预览模式"),
        (LyricDisplayMode.BILINGUAL_SYNC, "双语同步显示")
    ]
    
    for mode, name in strategies:
        timeline.set_display_mode(mode)
        info = timeline.get_info()
        strategy_info = info.get('strategy_info', {})
        print(f"   {name}: {strategy_info.get('strategy_type', 'Unknown')}")

def demo_new_vs_old_interface():
    """演示新旧接口对比"""
    print("\n=== 新旧接口对比演示 ===")
    
    from enhanced_generator import EnhancedJingwuGenerator
    from lyric_timeline import LyricTimeline, LyricDisplayMode
    
    generator = EnhancedJingwuGenerator(width=720, height=1280)
    
    test_lyrics = [
        (0.0, "对比测试1"),
        (3.0, "对比测试2")
    ]
    
    print("1. 传统方式（向后兼容）")
    print("   generator.generate_bilingual_video(main_lyrics=lyrics, ...)")
    
    print("\n2. 新的OOP方式（推荐）")
    timeline = LyricTimeline(
        lyrics_data=test_lyrics,
        language="chinese",
        display_mode=LyricDisplayMode.ENHANCED_PREVIEW
    )
    print("   timeline = LyricTimeline(...)")
    print("   generator.generate_bilingual_video(main_timeline=timeline, ...)")
    
    print("\n3. 新方式的优势:")
    print("   - 更好的封装性：歌词数据和显示逻辑封装在一起")
    print("   - 策略模式：可插拔的显示策略")
    print("   - 自报告尺寸：timeline.calculate_required_rect()")
    print("   - 类型安全：强类型定义")
    print("   - 易于扩展：添加新策略只需实现接口")

def main():
    """主演示函数"""
    print("LyricTimeline OOP重构功能演示")
    print("=" * 60)
    
    demos = [
        demo_basic_usage,
        demo_lrc_file_usage,
        demo_enhanced_generator_integration,
        demo_strategy_pattern,
        demo_new_vs_old_interface
    ]
    
    for demo_func in demos:
        try:
            demo_func()
        except Exception as e:
            print(f"   ERROR: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("演示完成！LyricTimeline OOP重构已成功实现！")
    print("\n主要特性:")
    print("- 面向对象的歌词时间轴设计")
    print("- 策略模式支持多种显示效果")
    print("- 自报告尺寸机制")
    print("- 完全向后兼容")
    print("- 类型安全和易于扩展")

if __name__ == "__main__":
    main()
