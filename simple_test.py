# -*- coding: utf-8 -*-
"""
简化的LyricTimeline测试脚本
"""

import sys
import traceback

def test_basic_import():
    """测试基本导入"""
    print("测试LyricTimeline模块导入...")

    try:
        from lyric_timeline import (
            LyricTimeline, LyricDisplayMode, LyricStyle, LyricRect,
            create_enhanced_timeline, create_simple_timeline
        )
        print("SUCCESS: LyricTimeline模块导入成功")
        return True
    except Exception as e:
        print(f"ERROR: 导入失败 - {e}")
        traceback.print_exc()
        return False

def test_basic_functionality():
    """测试基本功能"""
    print("\n测试基本功能...")

    try:
        from lyric_timeline import create_enhanced_timeline, create_simple_timeline

        # 测试数据
        test_lyrics = [
            (0.0, "第一句歌词"),
            (3.0, "第二句歌词"),
            (6.0, "第三句歌词")
        ]

        # 创建增强时间轴
        enhanced_timeline = create_enhanced_timeline(test_lyrics, "chinese")
        print("SUCCESS: 增强预览模式时间轴创建成功")

        # 获取信息
        info = enhanced_timeline.get_info()
        print(f"INFO: 时间轴信息 - 语言: {info['language']}, 行数: {info['total_lines']}")

        # 计算区域
        rect = enhanced_timeline.calculate_required_rect(1280, 720)
        print(f"INFO: 计算区域 - x:{rect.x}, y:{rect.y}, w:{rect.width}, h:{rect.height}")

        # 创建简单时间轴
        simple_timeline = create_simple_timeline(test_lyrics, "english")
        print("SUCCESS: 简单模式时间轴创建成功")

        return True

    except Exception as e:
        print(f"ERROR: 功能测试失败 - {e}")
        traceback.print_exc()
        return False

def test_enhanced_generator():
    """测试与enhanced_generator的集成"""
    print("\n测试enhanced_generator集成...")

    try:
        from enhanced_generator import EnhancedJingwuGenerator

        print("INFO: 使用纯OOP接口（LYRIC_TIMELINE_AVAILABLE标志已移除）")

        # 创建生成器
        generator = EnhancedJingwuGenerator(width=720, height=1280)
        print("SUCCESS: EnhancedJingwuGenerator创建成功")

        # 测试数据
        test_lyrics = [
            (0.0, "测试歌词1"),
            (3.0, "测试歌词2")
        ]

        # 直接创建时间轴（因为便捷方法已移除）
        from lyric_timeline import LyricTimeline, LyricDisplayMode
        timeline = LyricTimeline(
            lyrics_data=test_lyrics,
            language="chinese",
            display_mode=LyricDisplayMode.ENHANCED_PREVIEW
        )
        print("SUCCESS: 直接创建时间轴成功")

        # 获取信息
        info = timeline.get_info()
        print(f"INFO: 时间轴信息 - {info}")

        return True

    except Exception as e:
        print(f"ERROR: 集成测试失败 - {e}")
        traceback.print_exc()
        return False

def test_lrc_parsing():
    """测试LRC文件解析"""
    print("\n测试LRC文件解析...")

    try:
        from lyric_timeline import LyricTimeline, LyricDisplayMode
        from pathlib import Path

        # 检查测试文件
        test_lrc = Path("精武英雄/精武英雄 - 甄子丹.lrc")
        if not test_lrc.exists():
            print(f"WARNING: 测试LRC文件不存在: {test_lrc}")
            return False

        print(f"INFO: 找到测试LRC文件: {test_lrc}")

        # 解析文件
        timeline = LyricTimeline.from_lrc_file(
            str(test_lrc),
            language="chinese",
            display_mode=LyricDisplayMode.ENHANCED_PREVIEW
        )
        print("SUCCESS: 从LRC文件创建时间轴成功")

        # 显示信息
        info = timeline.get_info()
        print(f"INFO: 歌词信息 - 语言:{info['language']}, 行数:{info['total_lines']}, 时长:{info['duration']:.1f}秒")

        return True

    except Exception as e:
        print(f"ERROR: LRC解析测试失败 - {e}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("LyricTimeline OOP重构测试")
    print("=" * 50)

    tests = [
        ("基本导入", test_basic_import),
        ("基本功能", test_basic_functionality),
        ("生成器集成", test_enhanced_generator),
        ("LRC解析", test_lrc_parsing)
    ]

    passed = 0
    total = len(tests)

    for test_name, test_func in tests:
        print(f"\n[{test_name}]")
        try:
            if test_func():
                passed += 1
                print(f"RESULT: {test_name} - PASSED")
            else:
                print(f"RESULT: {test_name} - FAILED")
        except Exception as e:
            print(f"RESULT: {test_name} - ERROR: {e}")

    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")

    if passed == total:
        print("所有测试通过！LyricTimeline OOP重构成功！")
        return True
    else:
        print("部分测试失败，需要进一步调试")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
