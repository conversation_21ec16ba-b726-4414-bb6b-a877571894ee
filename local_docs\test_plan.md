# LyricTimeline测试方案

## 概述

本文档详细描述了LyricTimeline OOP重构的完整测试策略，包括单元测试、集成测试、性能测试和回归测试。确保重构后的代码质量和功能完整性。

## 测试策略

### 测试原则
1. **全面覆盖**: 覆盖所有核心功能和边界情况
2. **分层测试**: 单元测试、集成测试、端到端测试
3. **自动化**: 所有测试都可以自动运行
4. **可重复**: 测试结果稳定可重复
5. **快速反馈**: 测试执行时间尽量短

### 测试分类
- **单元测试**: 测试单个类和方法
- **集成测试**: 测试类之间的交互
- **功能测试**: 测试完整的功能流程
- **性能测试**: 测试性能指标
- **回归测试**: 确保现有功能不被破坏

## 单元测试设计

### 1. LyricStyle测试

```python
# test_lyric_style.py
import unittest
from lyric_timeline import LyricStyle

class TestLyricStyle(unittest.TestCase):
    
    def test_default_values(self):
        """测试默认值"""
        style = LyricStyle()
        self.assertEqual(style.font_size, 80)
        self.assertEqual(style.font_color, 'white')
        self.assertEqual(style.highlight_color, '#FFD700')
        self.assertEqual(style.animation_style, 'fade')
    
    def test_custom_values(self):
        """测试自定义值"""
        style = LyricStyle(
            font_size=100,
            font_color='red',
            highlight_color='blue',
            animation_style='slide'
        )
        self.assertEqual(style.font_size, 100)
        self.assertEqual(style.font_color, 'red')
        self.assertEqual(style.highlight_color, 'blue')
        self.assertEqual(style.animation_style, 'slide')
    
    def test_copy(self):
        """测试样式复制"""
        original = LyricStyle(font_size=90, font_color='green')
        copied = original.copy()
        
        self.assertEqual(copied.font_size, 90)
        self.assertEqual(copied.font_color, 'green')
        
        # 修改复制品不应影响原始对象
        copied.font_size = 120
        self.assertEqual(original.font_size, 90)
```

### 2. LyricRect测试

```python
# test_lyric_rect.py
import unittest
from lyric_timeline import LyricRect

class TestLyricRect(unittest.TestCase):
    
    def test_valid_rect(self):
        """测试有效矩形"""
        rect = LyricRect(10, 20, 100, 50)
        self.assertEqual(rect.x, 10)
        self.assertEqual(rect.y, 20)
        self.assertEqual(rect.width, 100)
        self.assertEqual(rect.height, 50)
    
    def test_invalid_dimensions(self):
        """测试无效尺寸"""
        with self.assertRaises(ValueError):
            LyricRect(0, 0, 0, 50)  # 宽度为0
        
        with self.assertRaises(ValueError):
            LyricRect(0, 0, 50, -10)  # 高度为负
    
    def test_contains_point(self):
        """测试点包含检测"""
        rect = LyricRect(10, 20, 100, 50)
        
        # 内部点
        self.assertTrue(rect.contains_point(50, 40))
        # 边界点
        self.assertTrue(rect.contains_point(10, 20))
        self.assertTrue(rect.contains_point(110, 70))
        # 外部点
        self.assertFalse(rect.contains_point(5, 15))
        self.assertFalse(rect.contains_point(120, 80))
    
    def test_overlaps_with(self):
        """测试重叠检测"""
        rect1 = LyricRect(10, 10, 50, 30)
        rect2 = LyricRect(40, 25, 50, 30)  # 重叠
        rect3 = LyricRect(70, 50, 50, 30)  # 不重叠
        
        self.assertTrue(rect1.overlaps_with(rect2))
        self.assertTrue(rect2.overlaps_with(rect1))
        self.assertFalse(rect1.overlaps_with(rect3))
        self.assertFalse(rect3.overlaps_with(rect1))
```

### 3. 显示策略测试

```python
# test_display_strategies.py
import unittest
from unittest.mock import Mock, MagicMock
from lyric_timeline import (
    SimpleFadeStrategy, 
    EnhancedPreviewStrategy,
    LyricTimeline,
    LyricDisplayMode,
    LyricStyle
)

class TestSimpleFadeStrategy(unittest.TestCase):
    
    def setUp(self):
        self.strategy = SimpleFadeStrategy(y_position=100, is_highlighted=True)
        self.timeline = LyricTimeline(
            [(0.0, "测试歌词1"), (3.0, "测试歌词2")],
            language="test",
            display_mode=LyricDisplayMode.SIMPLE_FADE
        )
    
    def test_calculate_required_rect(self):
        """测试区域计算"""
        rect = self.strategy.calculate_required_rect(self.timeline, 1280, 720)
        
        self.assertEqual(rect.x, 0)
        self.assertEqual(rect.width, 1280)
        self.assertGreater(rect.height, 0)
        # y位置应该在指定位置附近
        self.assertAlmostEqual(rect.y + rect.height // 2, 100, delta=50)
    
    def test_generate_clips(self):
        """测试片段生成"""
        mock_generator = Mock()
        mock_generator.width = 1280
        mock_generator.height = 720
        mock_generator.create_lyric_clip_with_animation = Mock(return_value=Mock())
        
        clips = self.strategy.generate_clips(self.timeline, mock_generator, 10.0)
        
        # 应该生成2个片段
        self.assertEqual(len(clips), 2)
        # 检查调用参数
        self.assertEqual(mock_generator.create_lyric_clip_with_animation.call_count, 2)

class TestEnhancedPreviewStrategy(unittest.TestCase):
    
    def setUp(self):
        self.strategy = EnhancedPreviewStrategy(
            current_y_offset=-50, 
            preview_y_offset=80
        )
        self.timeline = LyricTimeline(
            [(0.0, "当前歌词"), (3.0, "下一句歌词"), (6.0, "第三句")],
            language="chinese",
            display_mode=LyricDisplayMode.ENHANCED_PREVIEW
        )
    
    def test_calculate_required_rect(self):
        """测试增强预览区域计算"""
        rect = self.strategy.calculate_required_rect(self.timeline, 1280, 720)
        
        self.assertEqual(rect.x, 0)
        self.assertEqual(rect.width, 1280)
        # 应该能容纳两行歌词
        self.assertGreater(rect.height, 100)
    
    def test_generate_clips_with_preview(self):
        """测试生成当前+预览片段"""
        mock_generator = Mock()
        mock_generator.width = 1280
        mock_generator.height = 720
        mock_generator.create_lyric_clip_with_animation = Mock(return_value=Mock())
        
        clips = self.strategy.generate_clips(self.timeline, mock_generator, 10.0)
        
        # 应该生成4个片段：2个当前歌词 + 2个预览歌词
        # (第三句没有预览，所以是2+2=4)
        expected_calls = 4
        self.assertEqual(len(clips), expected_calls)
        self.assertEqual(mock_generator.create_lyric_clip_with_animation.call_count, expected_calls)
        
        # 检查高亮参数：奇数索引是当前歌词(True)，偶数索引是预览(False)
        calls = mock_generator.create_lyric_clip_with_animation.call_args_list
        self.assertTrue(calls[0][1]['is_highlighted'])   # 第一句当前
        self.assertFalse(calls[1][1]['is_highlighted'])  # 第二句预览
        self.assertTrue(calls[2][1]['is_highlighted'])   # 第二句当前
        self.assertFalse(calls[3][1]['is_highlighted'])  # 第三句预览
```

### 4. LyricTimeline主类测试

```python
# test_lyric_timeline.py
import unittest
import tempfile
import os
from lyric_timeline import LyricTimeline, LyricDisplayMode, LyricStyle

class TestLyricTimeline(unittest.TestCase):
    
    def setUp(self):
        self.test_lyrics = [
            (0.0, "第一句歌词"),
            (3.5, "第二句歌词"),
            (7.2, "第三句歌词"),
            (10.8, "第四句歌词")
        ]
    
    def test_initialization(self):
        """测试初始化"""
        timeline = LyricTimeline(
            self.test_lyrics,
            language="chinese",
            display_mode=LyricDisplayMode.ENHANCED_PREVIEW
        )
        
        self.assertEqual(timeline.language, "chinese")
        self.assertEqual(timeline.display_mode, LyricDisplayMode.ENHANCED_PREVIEW)
        self.assertEqual(len(timeline.lyrics_data), 4)
        self.assertIsNotNone(timeline._strategy)
    
    def test_lyrics_sorting(self):
        """测试歌词按时间排序"""
        unsorted_lyrics = [
            (5.0, "第二句"),
            (1.0, "第一句"),
            (10.0, "第三句")
        ]
        timeline = LyricTimeline(unsorted_lyrics)
        
        # 应该按时间戳排序
        self.assertEqual(timeline.lyrics_data[0][0], 1.0)
        self.assertEqual(timeline.lyrics_data[1][0], 5.0)
        self.assertEqual(timeline.lyrics_data[2][0], 10.0)
    
    def test_set_display_mode(self):
        """测试设置显示模式"""
        timeline = LyricTimeline(self.test_lyrics)
        
        # 切换到增强预览模式
        timeline.set_display_mode(
            LyricDisplayMode.ENHANCED_PREVIEW,
            current_y_offset=-60,
            preview_y_offset=100
        )
        
        self.assertEqual(timeline.display_mode, LyricDisplayMode.ENHANCED_PREVIEW)
        self.assertEqual(timeline._strategy.current_y_offset, -60)
        self.assertEqual(timeline._strategy.preview_y_offset, 100)
    
    def test_get_filtered_lyrics(self):
        """测试歌词过滤"""
        timeline = LyricTimeline(self.test_lyrics)
        
        # 获取前5秒的歌词
        filtered = timeline.get_filtered_lyrics(5.0)
        self.assertEqual(len(filtered), 2)  # 0.0s 和 3.5s
        
        # 获取前8秒的歌词
        filtered = timeline.get_filtered_lyrics(8.0)
        self.assertEqual(len(filtered), 3)  # 0.0s, 3.5s, 7.2s
    
    def test_get_info(self):
        """测试信息获取"""
        style = LyricStyle(font_size=90, font_color='red')
        timeline = LyricTimeline(
            self.test_lyrics,
            language="test",
            style=style,
            display_mode=LyricDisplayMode.SIMPLE_FADE
        )
        
        info = timeline.get_info()
        
        self.assertEqual(info['language'], 'test')
        self.assertEqual(info['total_lines'], 4)
        self.assertEqual(info['duration'], 10.8)
        self.assertEqual(info['display_mode'], 'simple_fade')
        self.assertIsNotNone(info['strategy_info'])
    
    def test_from_lrc_file(self):
        """测试从LRC文件创建"""
        # 创建临时LRC文件
        lrc_content = """[00:00.00]第一句歌词
[00:03.50]第二句歌词
[00:07.20]第三句歌词
[00:10.80]第四句歌词
"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.lrc', delete=False, encoding='utf-8') as f:
            f.write(lrc_content)
            temp_path = f.name
        
        try:
            timeline = LyricTimeline.from_lrc_file(
                temp_path,
                language="chinese",
                display_mode=LyricDisplayMode.ENHANCED_PREVIEW
            )
            
            self.assertEqual(timeline.language, "chinese")
            self.assertEqual(len(timeline.lyrics_data), 4)
            self.assertEqual(timeline.lyrics_data[0][1], "第一句歌词")
            self.assertEqual(timeline.lyrics_data[1][0], 3.5)
            
        finally:
            os.unlink(temp_path)
```

## 集成测试设计

### 1. 策略切换测试

```python
# test_integration.py
import unittest
from unittest.mock import Mock
from lyric_timeline import LyricTimeline, LyricDisplayMode

class TestIntegration(unittest.TestCase):
    
    def test_strategy_switching(self):
        """测试策略切换的完整流程"""
        timeline = LyricTimeline(
            [(0.0, "测试1"), (3.0, "测试2")],
            display_mode=LyricDisplayMode.SIMPLE_FADE
        )
        
        mock_generator = Mock()
        mock_generator.width = 1280
        mock_generator.height = 720
        mock_generator.create_lyric_clip_with_animation = Mock(return_value=Mock())
        
        # 简单模式生成
        clips1 = timeline.generate_clips(mock_generator, 10.0)
        call_count1 = mock_generator.create_lyric_clip_with_animation.call_count
        
        # 切换到增强预览模式
        mock_generator.reset_mock()
        timeline.set_display_mode(LyricDisplayMode.ENHANCED_PREVIEW)
        clips2 = timeline.generate_clips(mock_generator, 10.0)
        call_count2 = mock_generator.create_lyric_clip_with_animation.call_count
        
        # 增强模式应该生成更多片段（当前+预览）
        self.assertGreater(call_count2, call_count1)
    
    def test_multiple_timelines_layout(self):
        """测试多个时间轴的布局"""
        timeline1 = LyricTimeline(
            [(0.0, "中文歌词")],
            language="chinese",
            display_mode=LyricDisplayMode.ENHANCED_PREVIEW
        )
        
        timeline2 = LyricTimeline(
            [(0.0, "English lyrics")],
            language="english",
            display_mode=LyricDisplayMode.SIMPLE_FADE
        )
        
        rect1 = timeline1.calculate_required_rect(1280, 720)
        rect2 = timeline2.calculate_required_rect(1280, 720)
        
        # 检查是否有重叠（这里可能需要手动调整位置）
        overlap = rect1.overlaps_with(rect2)
        print(f"时间轴重叠: {overlap}")
        print(f"时间轴1区域: {rect1}")
        print(f"时间轴2区域: {rect2}")
```

## 性能测试设计

### 1. 基准性能测试

```python
# test_performance.py
import unittest
import time
from lyric_timeline import LyricTimeline, LyricDisplayMode

class TestPerformance(unittest.TestCase):
    
    def setUp(self):
        # 创建大量歌词数据
        self.large_lyrics = [(i * 2.0, f"歌词{i}") for i in range(1000)]
    
    def test_timeline_creation_performance(self):
        """测试时间轴创建性能"""
        start_time = time.time()
        
        for _ in range(100):
            timeline = LyricTimeline(
                self.large_lyrics,
                display_mode=LyricDisplayMode.ENHANCED_PREVIEW
            )
        
        end_time = time.time()
        avg_time = (end_time - start_time) / 100
        
        print(f"平均创建时间: {avg_time:.4f}秒")
        self.assertLess(avg_time, 0.1)  # 应该在100ms内
    
    def test_clip_generation_performance(self):
        """测试片段生成性能"""
        timeline = LyricTimeline(
            self.large_lyrics,
            display_mode=LyricDisplayMode.ENHANCED_PREVIEW
        )
        
        mock_generator = Mock()
        mock_generator.width = 1280
        mock_generator.height = 720
        mock_generator.create_lyric_clip_with_animation = Mock(return_value=Mock())
        
        start_time = time.time()
        clips = timeline.generate_clips(mock_generator, 2000.0)  # 2000秒
        end_time = time.time()
        
        generation_time = end_time - start_time
        print(f"生成{len(clips)}个片段耗时: {generation_time:.4f}秒")
        self.assertLess(generation_time, 1.0)  # 应该在1秒内
```

## 回归测试设计

### 1. 向后兼容性测试

```python
# test_backward_compatibility.py
import unittest
from unittest.mock import Mock, patch
import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

class TestBackwardCompatibility(unittest.TestCase):
    
    def test_old_interface_still_works(self):
        """测试旧接口仍然可用"""
        # 这个测试需要在集成后运行
        # 模拟旧的调用方式
        pass
    
    def test_output_consistency(self):
        """测试输出一致性"""
        # 比较新旧实现的输出结果
        pass
```

## 测试数据准备

### 1. 测试用例数据

```python
# test_data.py
"""测试数据定义"""

# 基础测试歌词
BASIC_LYRICS = [
    (0.0, "第一句歌词"),
    (3.5, "第二句歌词"),
    (7.2, "第三句歌词")
]

# 边界情况测试
EDGE_CASE_LYRICS = [
    (0.0, ""),  # 空歌词
    (0.01, "很短时间"),  # 极短时间间隔
    (100.0, "很长的歌词文本，包含各种字符：中文、English、123、!@#$%"),
]

# 大数据量测试
LARGE_LYRICS = [(i * 2.0, f"歌词行{i}") for i in range(1000)]

# LRC文件内容
SAMPLE_LRC_CONTENT = """[ti:测试歌曲]
[ar:测试歌手]
[al:测试专辑]
[00:00.00]第一句歌词
[00:03.50]第二句歌词
[00:07.20]第三句歌词
[00:10.80]第四句歌词
"""
```

## 测试执行计划

### 1. 自动化测试脚本

```python
# run_tests.py
"""测试执行脚本"""
import unittest
import sys
import os

def run_all_tests():
    """运行所有测试"""
    # 发现并运行所有测试
    loader = unittest.TestLoader()
    start_dir = 'tests'
    suite = loader.discover(start_dir, pattern='test_*.py')
    
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 生成测试报告
    print(f"\n测试总结:")
    print(f"运行测试: {result.testsRun}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    print(f"跳过: {len(result.skipped)}")
    
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
```

### 2. 持续集成配置

```yaml
# .github/workflows/test.yml
name: Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: 3.8
    - name: Install dependencies
      run: |
        pip install -r requirements.txt
        pip install pytest pytest-cov
    - name: Run tests
      run: |
        python run_tests.py
        pytest --cov=lyric_timeline tests/
```

## 测试覆盖率目标

### 覆盖率要求
- **单元测试覆盖率**: ≥ 90%
- **集成测试覆盖率**: ≥ 80%
- **关键路径覆盖率**: 100%

### 覆盖率检查
```bash
# 生成覆盖率报告
pytest --cov=lyric_timeline --cov-report=html tests/

# 检查覆盖率
coverage report --fail-under=90
```

## 总结

这个测试方案提供了全面的测试覆盖，确保LyricTimeline重构的质量和稳定性。通过分层的测试策略，可以在开发过程中及时发现问题，保证代码质量。

测试方案的关键特点：
1. **全面性**: 覆盖所有核心功能
2. **自动化**: 可以自动运行和验证
3. **可维护**: 测试代码清晰易懂
4. **高效性**: 快速反馈测试结果
