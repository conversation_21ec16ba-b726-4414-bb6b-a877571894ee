# LyricTimeline/LrcChannel 类型设计文档

## 概述

本文档详细描述了将现有歌词处理逻辑重构为面向对象设计的完整方案。通过引入 `LyricTimeline` 类型和策略模式，实现更好的代码组织、可扩展性和可维护性。

## 设计目标

### 主要目标
1. **封装歌词数据和显示逻辑**: 将歌词时间轴数据与其显示方法封装在一起
2. **策略模式实现**: 支持多种显示模式的可插拔实现
3. **自报告尺寸**: 每个时间轴能够计算并报告所需的显示区域
4. **向后兼容**: 保持现有API的兼容性
5. **可扩展性**: 易于添加新的显示效果和动画

### 核心原则
- **单一职责**: 每个类专注于特定功能
- **开闭原则**: 对扩展开放，对修改封闭
- **依赖倒置**: 依赖抽象而非具体实现
- **组合优于继承**: 使用策略模式而非继承层次

## 类图设计

```mermaid
classDiagram
    class LyricTimeline {
        -lyrics_data: List[Tuple[float, str]]
        -language: str
        -style: LyricStyle
        -display_mode: LyricDisplayMode
        -strategy: LyricDisplayStrategy
        +__init__(lyrics_data, language, style, display_mode)
        +set_display_mode(mode, **kwargs)
        +calculate_required_rect(video_width, video_height): LyricRect
        +generate_clips(generator, duration): List[ImageClip]
        +get_filtered_lyrics(max_duration): List[Tuple[float, str]]
        +get_info(): Dict[str, Any]
        +from_lrc_file(lrc_path, language, display_mode): LyricTimeline
    }

    class LyricDisplayStrategy {
        <<abstract>>
        +calculate_required_rect(timeline, video_width, video_height): LyricRect
        +generate_clips(timeline, generator, duration): List[ImageClip]
    }

    class SimpleFadeStrategy {
        -y_position: Optional[int]
        -is_highlighted: bool
        +__init__(y_position, is_highlighted)
        +calculate_required_rect(timeline, video_width, video_height): LyricRect
        +generate_clips(timeline, generator, duration): List[ImageClip]
    }

    class EnhancedPreviewStrategy {
        -current_y_offset: int
        -preview_y_offset: int
        +__init__(current_y_offset, preview_y_offset)
        +calculate_required_rect(timeline, video_width, video_height): LyricRect
        +generate_clips(timeline, generator, duration): List[ImageClip]
    }

    class BilingualSyncStrategy {
        -main_y_offset: int
        -aux_y_offset: int
        +__init__(main_y_offset, aux_y_offset)
        +calculate_required_rect(timeline, video_width, video_height): LyricRect
        +generate_clips(timeline, generator, duration): List[ImageClip]
    }

    class LyricStyle {
        +font_size: int
        +font_color: str
        +highlight_color: str
        +shadow_color: Tuple[int, int, int, int]
        +glow_enabled: bool
        +animation_style: str
    }

    class LyricRect {
        +x: int
        +y: int
        +width: int
        +height: int
        +__post_init__()
    }

    class LyricDisplayMode {
        <<enumeration>>
        SIMPLE_FADE
        ENHANCED_PREVIEW
        BILINGUAL_SYNC
        KARAOKE_STYLE
    }

    LyricTimeline --> LyricDisplayStrategy
    LyricTimeline --> LyricStyle
    LyricTimeline --> LyricDisplayMode
    LyricDisplayStrategy --> LyricRect
    LyricDisplayStrategy <|-- SimpleFadeStrategy
    LyricDisplayStrategy <|-- EnhancedPreviewStrategy
    LyricDisplayStrategy <|-- BilingualSyncStrategy
```

## 核心接口定义

### LyricTimeline 主类

```python
class LyricTimeline:
    """歌词时间轴类 - 封装歌词数据和显示逻辑"""
    
    def __init__(self, lyrics_data: List[Tuple[float, str]], 
                 language: str = "unknown",
                 style: Optional[LyricStyle] = None,
                 display_mode: LyricDisplayMode = LyricDisplayMode.SIMPLE_FADE):
        """初始化歌词时间轴"""
        
    def set_display_mode(self, mode: LyricDisplayMode, **kwargs) -> None:
        """设置显示模式和相关参数"""
        
    def calculate_required_rect(self, video_width: int, video_height: int) -> LyricRect:
        """计算所需的显示区域"""
        
    def generate_clips(self, generator: Any, duration: float) -> List[ImageClip]:
        """生成视频片段列表"""
        
    def get_filtered_lyrics(self, max_duration: float) -> List[Tuple[float, str]]:
        """获取指定时长内的歌词数据"""
        
    @classmethod
    def from_lrc_file(cls, lrc_path: str, language: str = "unknown", 
                     display_mode: LyricDisplayMode = LyricDisplayMode.SIMPLE_FADE) -> 'LyricTimeline':
        """从LRC文件创建时间轴实例"""
```

### LyricDisplayStrategy 抽象基类

```python
class LyricDisplayStrategy(ABC):
    """歌词显示策略抽象基类"""
    
    @abstractmethod
    def calculate_required_rect(self, timeline: 'LyricTimeline', 
                              video_width: int, video_height: int) -> LyricRect:
        """计算显示策略所需的区域"""
        
    @abstractmethod
    def generate_clips(self, timeline: 'LyricTimeline', 
                      generator: Any, duration: float) -> List[ImageClip]:
        """生成符合策略的视频片段"""
```

## 策略模式实现

### 1. SimpleFadeStrategy (简单淡入淡出)

**用途**: 单行歌词的简单显示，支持淡入淡出效果
**特点**: 
- 单行显示
- 可配置位置和高亮状态
- 适用于副歌词或简单场景

### 2. EnhancedPreviewStrategy (增强预览模式)

**用途**: 当前歌词+下一句预览的增强显示
**特点**:
- 当前歌词高亮显示
- 下一句歌词预览（非高亮）
- 可配置两行的垂直偏移
- 封装现有的增强模式逻辑

**核心逻辑**:
```python
# 当前歌词（高亮）
current_clip = generator.create_lyric_clip_with_animation(
    text, start_time, lyric_duration,
    is_highlighted=True,
    y_position=center_y + self.current_y_offset,
    animation=timeline.style.animation_style
)

# 下一句预览（非高亮）
if has_next_lyric:
    preview_clip = generator.create_lyric_clip_with_animation(
        next_text, start_time, lyric_duration,
        is_highlighted=False,
        y_position=center_y + self.preview_y_offset,
        animation='fade'
    )
```

### 3. BilingualSyncStrategy (双语同步显示)

**用途**: 双语歌词的同步显示
**特点**:
- 主歌词和副歌词同时显示
- 可配置两种语言的位置
- 支持不同的高亮策略

## 数据结构定义

### LyricStyle (样式配置)

```python
@dataclass
class LyricStyle:
    """歌词样式配置"""
    font_size: int = 80
    font_color: str = 'white'
    highlight_color: str = '#FFD700'
    shadow_color: Tuple[int, int, int, int] = (0, 0, 0, 200)
    glow_enabled: bool = False
    animation_style: str = 'fade'
```

### LyricRect (位置尺寸)

```python
@dataclass
class LyricRect:
    """歌词显示区域信息"""
    x: int
    y: int
    width: int
    height: int
    
    def __post_init__(self):
        if self.width <= 0 or self.height <= 0:
            raise ValueError("宽度和高度必须大于0")
```

### LyricDisplayMode (显示模式枚举)

```python
class LyricDisplayMode(Enum):
    SIMPLE_FADE = "simple_fade"           # 简单淡入淡出
    ENHANCED_PREVIEW = "enhanced_preview"  # 增强模式：当前+预览
    BILINGUAL_SYNC = "bilingual_sync"     # 双语同步显示
    KARAOKE_STYLE = "karaoke_style"       # 卡拉OK样式（未来扩展）
```

## 使用示例

### 基本使用

```python
# 创建中文歌词时间轴（增强预览模式）
chinese_timeline = LyricTimeline(
    lyrics_data=chinese_lyrics,
    language="chinese",
    style=LyricStyle(font_size=80, highlight_color='#FFD700'),
    display_mode=LyricDisplayMode.ENHANCED_PREVIEW
)

# 创建英文歌词时间轴（简单淡入淡出）
english_timeline = LyricTimeline(
    lyrics_data=english_lyrics,
    language="english",
    display_mode=LyricDisplayMode.SIMPLE_FADE
)

# 计算所需区域
chinese_rect = chinese_timeline.calculate_required_rect(1280, 720)
english_rect = english_timeline.calculate_required_rect(1280, 720)

# 生成视频片段
chinese_clips = chinese_timeline.generate_clips(generator, duration=180.0)
english_clips = english_timeline.generate_clips(generator, duration=180.0)
```

### 从文件创建

```python
# 从LRC文件创建时间轴
timeline = LyricTimeline.from_lrc_file(
    "song.lrc", 
    language="chinese",
    display_mode=LyricDisplayMode.ENHANCED_PREVIEW
)
```

### 动态切换显示模式

```python
# 运行时切换显示模式
timeline.set_display_mode(
    LyricDisplayMode.ENHANCED_PREVIEW,
    current_y_offset=-60,
    preview_y_offset=100
)
```

## 扩展性设计

### 添加新的显示策略

```python
class KaraokeStrategy(LyricDisplayStrategy):
    """卡拉OK样式策略"""
    
    def calculate_required_rect(self, timeline, video_width, video_height):
        # 实现卡拉OK样式的区域计算
        pass
        
    def generate_clips(self, timeline, generator, duration):
        # 实现卡拉OK样式的片段生成
        pass
```

### 布局管理器（未来扩展）

```python
class LyricLayoutManager:
    """管理多个歌词时间轴的布局"""
    
    def calculate_layout(self, timelines: List[LyricTimeline], 
                        video_width: int, video_height: int) -> Dict[LyricTimeline, LyricRect]:
        """自动计算最优布局，避免重叠"""
        pass
```

## 性能考虑

1. **延迟初始化**: 策略对象在需要时才创建
2. **缓存机制**: 重复计算的结果可以缓存
3. **内存管理**: 及时释放不需要的视频片段
4. **批量处理**: 支持批量生成多个时间轴的片段

## 总结

这个设计方案通过引入 `LyricTimeline` 类型和策略模式，实现了：

1. **更好的封装**: 歌词数据和显示逻辑封装在一起
2. **灵活的扩展**: 通过策略模式支持多种显示效果
3. **自动布局**: 每个时间轴能够报告所需空间
4. **类型安全**: 强类型定义减少错误
5. **向后兼容**: 保持现有API的兼容性

这个设计为歌词视频生成器提供了坚实的面向对象基础，支持未来的功能扩展和维护。
