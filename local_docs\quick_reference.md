# LyricTimeline 快速参考

## 核心类型速查

### LyricTimeline 主类
```python
# 创建时间轴
timeline = LyricTimeline(
    lyrics_data=[(0.0, "歌词1"), (3.0, "歌词2")],
    language="chinese",
    style=LyricStyle(font_size=80),
    display_mode=LyricDisplayMode.ENHANCED_PREVIEW
)

# 从文件创建
timeline = LyricTimeline.from_lrc_file("song.lrc", "chinese")

# 核心方法
rect = timeline.calculate_required_rect(1280, 720)  # 计算所需区域
clips = timeline.generate_clips(generator, 180.0)   # 生成视频片段
info = timeline.get_info()                          # 获取时间轴信息
```

### 显示模式
```python
# 简单淡入淡出
LyricDisplayMode.SIMPLE_FADE

# 增强预览（当前+下一句）
LyricDisplayMode.ENHANCED_PREVIEW

# 双语同步
LyricDisplayMode.BILINGUAL_SYNC
```

### 策略配置
```python
# 增强预览策略
timeline.set_display_mode(
    LyricDisplayMode.ENHANCED_PREVIEW,
    current_y_offset=-50,    # 当前歌词位置偏移
    preview_y_offset=80      # 预览歌词位置偏移
)

# 简单模式策略
timeline.set_display_mode(
    LyricDisplayMode.SIMPLE_FADE,
    y_position=360,          # 固定Y位置
    is_highlighted=True      # 是否高亮
)
```

## 集成到现有代码

### 新接口（推荐）
```python
# 创建时间轴对象
chinese_timeline = LyricTimeline.from_lrc_file("chinese.lrc", "chinese")
english_timeline = LyricTimeline.from_lrc_file("english.lrc", "english")

# 设置显示模式
chinese_timeline.set_display_mode(LyricDisplayMode.ENHANCED_PREVIEW)
english_timeline.set_display_mode(LyricDisplayMode.SIMPLE_FADE)

# 生成视频
generator.generate_bilingual_video(
    main_timeline=chinese_timeline,
    aux_timeline=english_timeline,
    audio_path="audio.mp3",
    output_path="output.mp4"
)
```

### 旧接口（兼容）
```python
# 现有代码无需修改，继续有效
generator.generate_bilingual_video(
    main_lyrics=chinese_lyrics,
    aux_lyrics=english_lyrics,
    audio_path="audio.mp3",
    output_path="output.mp4"
)
```

## 常用模式

### 增强模式（当前+预览）
```python
timeline = create_enhanced_timeline(lyrics_data, "chinese")
# 等价于：
timeline = LyricTimeline(
    lyrics_data=lyrics_data,
    language="chinese",
    display_mode=LyricDisplayMode.ENHANCED_PREVIEW
)
```

### 双语模式
```python
chinese_timeline = LyricTimeline(chinese_lyrics, "chinese")
english_timeline = LyricTimeline(english_lyrics, "english")

# 设置双语布局
chinese_timeline.set_display_mode(
    LyricDisplayMode.BILINGUAL_SYNC,
    main_y_offset=-80
)
english_timeline.set_display_mode(
    LyricDisplayMode.BILINGUAL_SYNC,
    aux_y_offset=60
)
```

### 布局检测
```python
rect1 = timeline1.calculate_required_rect(1280, 720)
rect2 = timeline2.calculate_required_rect(1280, 720)

if rect1.overlaps_with(rect2):
    print("警告：歌词区域重叠！")
    # 调整位置或显示模式
```

## 样式配置

### 基础样式
```python
style = LyricStyle(
    font_size=80,
    font_color='white',
    highlight_color='#FFD700',
    shadow_color=(0, 0, 0, 200),
    glow_enabled=True,
    animation_style='fade'
)

timeline = LyricTimeline(lyrics, style=style)
```

### 预设样式
```python
# 经典样式
classic_style = LyricStyle(
    font_color='white',
    highlight_color='#FFD700',
    glow_enabled=True
)

# 现代样式
modern_style = LyricStyle(
    font_color='#333333',
    highlight_color='#00FF00',
    glow_enabled=False
)
```

## 实施检查清单

### 阶段1：核心实现 ✓
- [ ] 复制 `lyric_timeline_template.py`
- [ ] 实现基础数据结构
- [ ] 实现 `SimpleFadeStrategy`
- [ ] 实现 `EnhancedPreviewStrategy`
- [ ] 运行单元测试

### 阶段2：主类实现 ✓
- [ ] 实现 `LyricTimeline` 主类
- [ ] 实现策略模式集成
- [ ] 实现工厂方法
- [ ] 测试所有公共接口

### 阶段3：集成现有代码 ✓
- [ ] 修改 `generate_bilingual_video` 函数
- [ ] 实现参数转换逻辑
- [ ] 保持向后兼容性
- [ ] 更新 demo 函数

### 阶段4：测试验证 ✓
- [ ] 运行所有现有测试
- [ ] 添加新的单元测试
- [ ] 验证输出一致性
- [ ] 性能基准测试

## 故障排除

### 常见问题

**Q: 新接口生成的视频与原来不一致？**
A: 检查策略参数设置，确保位置偏移与原实现匹配。

**Q: 时间轴区域重叠怎么办？**
A: 使用 `calculate_required_rect()` 检测，调整 `y_offset` 参数。

**Q: 性能比原来慢？**
A: 检查是否有不必要的重复计算，考虑添加缓存。

**Q: 现有代码报错？**
A: 确保保持了向后兼容性，检查参数转换逻辑。

### 调试技巧

```python
# 查看时间轴信息
print(timeline.get_info())

# 检查策略状态
print(timeline._strategy.get_strategy_info())

# 验证区域计算
rect = timeline.calculate_required_rect(1280, 720)
print(f"所需区域: {rect}")

# 测试片段生成
clips = timeline.generate_clips(mock_generator, 30.0)
print(f"生成片段数: {len(clips)}")
```

## 扩展示例

### 自定义策略
```python
class CustomStrategy(LyricDisplayStrategy):
    def calculate_required_rect(self, timeline, width, height):
        # 自定义区域计算
        return LyricRect(0, height//3, width, height//3)
    
    def generate_clips(self, timeline, generator, duration):
        # 自定义片段生成
        clips = []
        # ... 实现逻辑
        return clips

# 使用自定义策略
timeline._strategy = CustomStrategy()
```

### 批量处理
```python
def process_multiple_songs(song_configs):
    for config in song_configs:
        timeline = LyricTimeline.from_lrc_file(config['lrc'], config['lang'])
        timeline.set_display_mode(config['mode'])
        
        generator.generate_bilingual_video(
            main_timeline=timeline,
            audio_path=config['audio'],
            output_path=config['output']
        )
```

## 版本兼容性

| 版本 | 兼容性 | 说明 |
|------|--------|------|
| v1.x | ✅ 完全兼容 | 所有现有API保持不变 |
| v2.0 | ✅ 向后兼容 | 新增OOP接口，旧接口继续有效 |
| v2.1+ | ⚠️ 弃用警告 | 旧接口添加弃用警告 |

## 性能指标

| 操作 | 目标时间 | 说明 |
|------|----------|------|
| 创建时间轴 | < 100ms | 1000行歌词 |
| 计算区域 | < 10ms | 单次调用 |
| 生成片段 | < 1s | 1000个片段 |
| 策略切换 | < 1ms | 运行时切换 |

---

**💡 提示**: 这个快速参考涵盖了最常用的功能。详细信息请查看完整文档。
