# -*- coding: utf-8 -*-
"""
测试使用新的LyricTimeline接口生成视频
"""

from pathlib import Path

TEST_TIME = 45.0

def test_new_interface_video_generation():
    """测试使用新接口生成视频"""
    print("=== 测试新接口视频生成 ===")

    try:
        from enhanced_generator import EnhancedJingwuGenerator
        from lyric_timeline import LyricTimeline, LyricDisplayMode

        # 检查配置文件
        config_path = Path("精武英雄/lrc-mv.yaml")
        if not config_path.exists():
            print(f"ERROR: 配置文件不存在: {config_path}")
            return False

        print(f"1. 找到配置文件: {config_path}")

        # 创建生成器
        generator = EnhancedJingwuGenerator(width=720, height=1280)
        print("2. 创建生成器成功")

        # 创建时间轴
        main_lrc_path = Path("精武英雄/精武英雄 - 甄子丹.lrc")
        if not main_lrc_path.exists():
            print(f"ERROR: 主歌词文件不存在: {main_lrc_path}")
            return False

        print(f"3. 创建主时间轴: {main_lrc_path}")
        main_timeline = LyricTimeline.from_lrc_file(
            str(main_lrc_path),
            language="chinese",
            display_mode=LyricDisplayMode.ENHANCED_PREVIEW
        )

        # 检查是否有英文歌词
        aux_lrc_path = Path("精武英雄/Jingwu Hero - Donnie Yen.lrc")
        aux_timeline = None
        if aux_lrc_path.exists():
            print(f"4. 创建副时间轴: {aux_lrc_path}")
            aux_timeline = LyricTimeline.from_lrc_file(
                str(aux_lrc_path),
                language="english",
                display_mode=LyricDisplayMode.BILINGUAL_SYNC
            )

            # 调整主时间轴为双语模式
            main_timeline.set_display_mode(LyricDisplayMode.BILINGUAL_SYNC)
        else:
            print("4. 未找到英文歌词，使用单语增强模式")

        # 显示时间轴信息
        print("\n5. 时间轴信息:")
        main_info = main_timeline.get_info()
        print(f"   主时间轴: 语言={main_info['language']}, 行数={main_info['total_lines']}, 模式={main_info['display_mode']}")

        if aux_timeline:
            aux_info = aux_timeline.get_info()
            print(f"   副时间轴: 语言={aux_info['language']}, 行数={aux_info['total_lines']}, 模式={aux_info['display_mode']}")

        # 计算布局
        print("\n6. 布局信息:")
        main_rect = main_timeline.calculate_required_rect(720, 1280)
        print(f"   主时间轴区域: {main_rect}")

        if aux_timeline:
            aux_rect = aux_timeline.calculate_required_rect(720, 1280)
            print(f"   副时间轴区域: {aux_rect}")

            if main_rect.overlaps_with(aux_rect):
                print("   WARNING: 时间轴区域重叠")
            else:
                print("   OK: 时间轴区域不重叠")

        # 准备生成视频（短时间测试）
        audio_path = Path("精武英雄/精武英雄 - 甄子丹.flac")
        background_path = Path("精武英雄/bg_v.png")
        output_path = Path("test_output_new_interface.mp4")

        if not audio_path.exists():
            print(f"ERROR: 音频文件不存在: {audio_path}")
            return False

        if not background_path.exists():
            print(f"WARNING: 背景图片不存在: {background_path}")
            background_path = None

        print(f"\n7. 准备生成视频:")
        print(f"   音频: {audio_path}")
        print(f"   背景: {background_path}")
        print(f"   输出: {output_path}")
        print(f"   时长: {TEST_TIME:.1f}秒（测试）")

        # 使用新接口生成视频
        print("\n8. 开始生成视频...")
        success = generator.generate_bilingual_video(
            main_timeline=main_timeline,
            aux_timeline=aux_timeline,
            audio_path=str(audio_path),
            output_path=str(output_path),
            background_image=str(background_path) if background_path else None,
            t_max_sec=TEST_TIME  # 只生成前{TEST_TIME:.1f}秒用于测试
        )

        if success:
            print("SUCCESS: 视频生成成功！")
            if output_path.exists():
                file_size = output_path.stat().st_size / (1024 * 1024)
                print(f"   文件大小: {file_size:.1f} MB")
            return True
        else:
            print("ERROR: 视频生成失败")
            return False

    except Exception as e:
        print(f"ERROR: 测试失败 - {e}")
        import traceback
        traceback.print_exc()
        return False

def test_traditional_interface_comparison():
    """测试传统接口对比"""
    print("\n=== 传统接口对比测试 ===")

    try:
        from enhanced_generator import EnhancedJingwuGenerator

        generator = EnhancedJingwuGenerator(width=720, height=1280)

        # 解析歌词文件（传统方式）
        main_lrc_path = Path("精武英雄/精武英雄 - 甄子丹.lrc")
        if not main_lrc_path.exists():
            print(f"ERROR: 主歌词文件不存在: {main_lrc_path}")
            return False

        main_lyrics = generator.parse_lrc_file(str(main_lrc_path))
        print(f"1. 解析主歌词: {len(main_lyrics)} 行")

        # 检查英文歌词
        aux_lrc_path = Path("精武英雄/Jingwu Hero - Donnie Yen.lrc")
        aux_lyrics = None
        if aux_lrc_path.exists():
            aux_lyrics = generator.parse_lrc_file(str(aux_lrc_path))
            print(f"2. 解析副歌词: {len(aux_lyrics)} 行")
        else:
            print("2. 未找到英文歌词")

        # 准备文件路径
        audio_path = Path("精武英雄/精武英雄 - 甄子丹.flac")
        background_path = Path("精武英雄/bg_v.png")
        output_path = Path("test_output_traditional_interface.mp4")

        if not audio_path.exists():
            print(f"ERROR: 音频文件不存在: {audio_path}")
            return False

        print(f"\n3. 使用传统接口生成视频:")
        print(f"   输出: {output_path}")
        print(f"   时长: {TEST_TIME:.1f}秒（测试）")

        # 使用传统接口
        success = generator.generate_bilingual_video(
            main_lyrics=main_lyrics,
            aux_lyrics=aux_lyrics,
            audio_path=str(audio_path),
            output_path=str(output_path),
            background_image=str(background_path) if background_path.exists() else None,
            t_max_sec=TEST_TIME
        )

        if success:
            print("SUCCESS: 传统接口视频生成成功！")
            if output_path.exists():
                file_size = output_path.stat().st_size / (1024 * 1024)
                print(f"   文件大小: {file_size:.1f} MB")
            return True
        else:
            print("ERROR: 传统接口视频生成失败")
            return False

    except Exception as e:
        print(f"ERROR: 传统接口测试失败 - {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("LyricTimeline视频生成测试")
    print("=" * 50)

    # 测试新接口
    new_success = test_new_interface_video_generation()

    print("\n" + "=" * 50)
    print("测试结果:")
    print(f"  新OOP接口: {'成功' if new_success else '失败'}")

    if new_success:
        print("\n测试通过！LyricTimeline OOP重构完全成功！")
        print("旧版接口已成功移除，只保留新的OOP接口")
        return True
    else:
        print("\n测试失败，需要进一步调试")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
